<template>

    <div class="layout-container">

      <div class="main-content">

        <router-view>
          <div class="right1">
            <el-button v-if="hasPassword === false" class="btn3" @click="openModal1('set')">设置密码</el-button>
            <el-button v-if="hasPassword === true" class="btn1" @click="openModal1('set')">修改密码</el-button>
          </div>
          <div class="right4">

            <div style="margin-left: 50px">
              <el-radio-group v-model="radio1" class="ml-4" v-if="radio1 !== ''">
                <el-radio label="0" size="large">开启</el-radio>
                <el-radio label="1" size="large">关闭</el-radio>
              </el-radio-group>
              <div v-else class="loading-placeholder">加载中...</div>
            </div>

            <!-- 左右两列布局 -->
            <div class="two-column-layout">
              <!-- 左列：量化值 -->
              <div class="left-column">
                <div class="right4-content">
                  <h1 class="function">量化值</h1>
                  <div class="line"></div>
                  <div class="input-row">
                    <span class="input-label">量化值:</span>
                    <el-input
                      v-if="sourceValue !== null"
                      class="input-field"
                      placeholder="请输入量化值"
                      v-model="sourceValue"
                      type="number"
                      :min="1"
                      :disabled="isQuantValueDisabled"
                      readonly
                    />
                    <div v-else class="loading-placeholder">加载中...</div>
                  </div>
                </div>
                <div class="right4-content">
                  <div class="input-row">
                    <span class="input-label">输入值:</span>
                    <el-input
                      class="input-field"
                      placeholder="请输入小于量化值的数值"
                      v-model="inputQuantValue"
                      type="number"
                      :min="1"
                      :max="sourceValue ? sourceValue - 1 : undefined"
                      :disabled="isQuantValueDisabled"
                    />
                  </div>
                </div>
                <div class="right4-content">
                  <div class="input-row">
                    <span class="input-label">企业名称:</span>
                    <el-select
                      v-model="selectedEnterpriseQuant"
                      class="input-field"
                      placeholder="请选择企业名称"
                      :disabled="isQuantValueDisabled"
                      filterable
                    >
                      <el-option
                        v-for="item in enterpriseOptions"
                        :key="item.tableName"
                        :label="item.name"
                        :value="item.tableName"
                      />
                    </el-select>
                  </div>
                </div>
                <div class="right4-content">
                  <div class="input-row">
                    <span class="input-label">手机号:</span>
                    <input
                      type="text"
                      v-model="phoneNumber"
                      placeholder="这里输入手机号"
                      class="input-field phone-input"
                      maxlength="11"
                      :disabled="isQuantValueDisabled"
                    />
                    <el-button @click="handleOutput" style="margin-left: 20px" :disabled="isQuantValueDisabled"
                      >输出</el-button
                    >
                  </div>
                </div>
              </div>

              <!-- 右列：补贴金 -->
              <div class="right-column">
                <div class="right4-content">
                  <h1 class="function">补贴金</h1>
                  <div class="line"></div>
                  <div class="input-row">
                    <span class="input-label">补贴金:</span>
                    <el-input
                      v-if="subsidyValue !== null"
                      class="input-field"
                      placeholder="请输入补贴金"
                      v-model="subsidyValue"
                      type="number"
                      :min="1"
                      :disabled="isQuantValueDisabled"
                      readonly
                    />
                    <div v-else class="loading-placeholder">加载中...</div>
                  </div>
                </div>
                <div class="right4-content">
                  <div class="input-row">
                    <span class="input-label">输入值:</span>
                    <el-input
                      class="input-field"
                      placeholder="请输入补贴金数值"
                      v-model="inputSubsidyValue"
                      type="number"
                      :min="1"
                      :max="subsidyValue"
                      :disabled="isQuantValueDisabled"
                    />
                  </div>
                </div>
                <div class="right4-content">
                  <div class="input-row">
                    <span class="input-label">企业名称:</span>
                    <el-select
                      v-model="selectedEnterpriseSubsidy"
                      class="input-field"
                      placeholder="请选择企业名称"
                      :disabled="isQuantValueDisabled"
                      filterable
                    >
                      <el-option
                        v-for="item in enterpriseOptions"
                        :key="item.tableName"
                        :label="item.name"
                        :value="item.tableName"
                      />
                    </el-select>
                  </div>
                </div>

                <div class="right4-content">
                  <div class="input-row">
                    <span class="input-label">手机号:</span>
                    <input
                      type="text"
                      v-model="subsidyPhoneNumber"
                      placeholder="这里输入手机号"
                      class="input-field phone-input"
                      maxlength="11"
                      :disabled="isQuantValueDisabled"
                    />
                    <el-button @click="handleSubsidyOutput" style="margin-left: 20px" :disabled="isQuantValueDisabled"
                      >输出</el-button
                    >
                  </div>
                </div>
              </div>
            </div>

            <div class="right4-content">
              <el-row style="height: 40px; line-height: 40px; text-align: left">

              </el-row>
            </div>
          </div>
          <div class="radio">
            <el-row style="margin-left: 50px; margin-top: 30px">
              <el-radio-group v-model="radio2" v-if="radio2 !== ''">
                <el-radio value="0" size="large">开启</el-radio>
                <el-radio value="1" size="large" style="margin-left: 20px"
                  >关闭</el-radio
                >
              </el-radio-group>
              <div v-else class="loading-placeholder">加载中...</div>
            </el-row>
          </div>
          <div class="right5">
            <div class="right5-top">
              <div class="data">功能数据</div>
              <div class="line"></div>
            </div>
            <div class="right5-footer">
              <!-- 企业名称独占一行 -->
              <div class="right5-content-full">
                <span style="padding-left: 30px; padding-top: 20px">
                  企业名称
                </span>
                <el-select
                  v-model="selectedEnterpriseFunction"
                  class="rate"
                  placeholder="请选择企业名称"
                  :disabled="isFunctionDataDisabled"
                  filterable
                >
                  <el-option
                    v-for="item in enterpriseOptions"
                    :key="item.tableName"
                    :label="item.name"
                    :value="item.tableName"
                  />
                </el-select>
              </div>
              <!-- 手机号和数值在同一行，确认按钮在右侧 -->
              <div class="right5-content-row">
                <div class="right5-content">
                  <span style="padding-left: 30px; padding-top: 20px">
                    请输入手机号
                  </span>
                  <el-input
                      class="rate"
                      placeholder="请输入手机号"
                      v-model="message9"
                      :disabled="isFunctionDataDisabled"
                  />
                </div>
                <div class="right5-content">
                  <span style="padding-left: 30px; padding-top: 20px">
                    请输入数值
                  </span>
                  <el-input
                      class="rate"
                      placeholder="正整数或小数，最多2位小数"
                      v-model="message10"
                      :disabled="isFunctionDataDisabled"
                  />
                </div>
                <div class="right5-content-btn">
                  <el-button type="primary" @click="handleFunctionDataConfirm" class="btn-confirm" :disabled="isFunctionDataDisabled"
                  >确认</el-button>
                </div>
              </div>

            </div>

            <!-- 密码模态框 -->
            <div class="container">
              <div v-if="showModal" class="modal-mask" @click.self="closeModal">
                <div class="modal-container">
                  <div class="modal-header">
                    <h3>输入密码</h3>
                    <button class="close-btn" @click="closeModal">
                      &times;
                    </button>
                  </div>
                  <div class="modal-body">
                    <input
                      type="password"
                      v-model="password"
                      placeholder="请输入密码"
                      @keyup.enter="handleSubmit"
                      ref="passwordInput"
                    />
                    <div v-if="errorMessage" class="error-msg">
                      {{ errorMessage }}
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button
                      class="confim-btn"
                      :disabled="isSubmitting"
                      @click="handleSubmit"
                    >
                      {{ isSubmitting ? "验证中..." : "确认" }}
                    </button>
                    <button class="cancel-btn" @click="closeModal">取消</button>
                  </div>
                </div>
              </div>
            </div>


            <!-- 遮罩层 -->
            <div
              v-if="showModal"
              class="overlay"
              @click.self="closeModal"
            ></div>

            <!-- 设置密码弹框 -->
            <div v-if="activeModal === 'set'" class="modal">
              <h3>设置新密码</h3>
              <form @submit.prevent="handleSetPassword">
                <div class="form-group">
                  <label>新密码:</label>
                  <input v-model="form.newPassword" type="password" required />
                </div>
                <div class="form-group">
                  <label>确认密码:</label>
                  <input
                    v-model="form.confirmPassword"
                    type="password"
                    required
                  />
                </div>
                <div class="button-group">
                  <button type="submit">确定</button>
                  <button type="button" @click="closeModal1">取消</button>
                </div>
              </form>
            </div>

            <!-- 修改密码弹框 -->
            <div v-if="activeModal === 'change'" class="modal">
              <h3>修改密码</h3>
              <form @submit.prevent="handleChangePassword">
                <!--div class="form-group">
                  <label>旧密码:</label>
                  <input v-model="form.oldPassword" type="password" required />
                </div>-->
                <div class="form-group">
                  <label>新密码:</label>
                  <input v-model="form.newPassword" type="password" required />
                </div>
                <div class="form-group">
                  <label>确认新密码:</label>
                  <input
                    v-model="form.confirmNewPassword"
                    type="password"
                    required
                  />
                </div>
                <div class="button-group">
                  <button type="submit">确定</button>
                  <button type="button" @click="closeModal1">取消</button>
                </div>
              </form>
            </div>
          </div>
        </router-view>
      </div>
    </div>

</template>
<script setup lang="ts">
import { nextTick, ref, onMounted, onUnmounted, reactive, computed, watch } from "vue";
import HomeBg from "../../components/HomeBg.vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import request from "../../utils/request";

// 定义API响应类型
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

const radio1 = ref(""); // 量化值开关状态 0-开启 1-关闭，初始为空避免闪烁
const radio2 = ref(""); // 功能数据开关状态 0-开启 1-关闭，初始为空避免闪烁
const index = ref();
const showModal = ref(false);
const showModal2 = ref(false);
const password = ref("");
const errorMessage = ref("");
const isSubmitting = ref(false);
const passwordInput = ref<HTMLInputElement | null>(null);
const showModal1 = ref(false);
const showModal3 = ref(false);
const activeModal = ref("");
const message9 = ref("");
const message10 = ref("");
const hasPassword = ref<boolean | null>(null); // 用户是否已设置密码，初始为null避免闪烁

// 量化值相关数据
const sourceValue = ref<number | null>(null); // 初始为null，等待API返回
const inputQuantValue = ref("");
const phoneNumber = ref("");

// 补贴金相关数据
const subsidyValue = ref<number | null>(null); // 初始为null，等待API返回
const inputSubsidyValue = ref("");
const subsidyPhoneNumber = ref("");

// 企业数据相关
const enterpriseOptions = ref<Array<{id: string, name: string, tableName: string}>>([]);
const selectedEnterpriseQuant = ref(""); // 量化值区域选择的企业
const selectedEnterpriseSubsidy = ref(""); // 补贴金区域选择的企业
const selectedEnterpriseFunction = ref(""); // 功能数据区域选择的企业

const form = reactive({
  oldPassword: "",
  newPassword: "",
  confirmNewPassword: "",
  confirmPassword: "",
});

const inputValue = ref<number | null>(null);
const inputValue2 = ref<number | null>(null);
const setPassword = ref("123");
const resultValue1 = ref("");
const inputRef = ref<HTMLInputElement | null>(null);
//输入结果显示
const resultValue = ref("");

// 校验逻辑：输入值必须 < sourceValue 且 > 0
const isQuantValueValid = computed(() => {
  const inputNum = parseFloat(inputQuantValue.value);
  const baseNum = sourceValue.value;

  return (
    baseNum !== null && !isNaN(inputNum) && inputNum > 0 && inputNum <= baseNum
  );
});

// 手机号验证
const isPhoneValid = computed(() => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phoneNumber.value);
});

// 补贴金验证逻辑：输入值必须 < subsidyValue 且 > 0
const isSubsidyValueValid = computed(() => {
  const inputNum = parseFloat(inputSubsidyValue.value);
  const baseNum = subsidyValue.value;

  return (
    baseNum !== null && !isNaN(inputNum) && inputNum > 0 && inputNum <= baseNum
  );
});

// 补贴金手机号验证
const isSubsidyPhoneValid = computed(() => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(subsidyPhoneNumber.value);
});

// 计算属性：量化值区域是否禁用
const isQuantValueDisabled = computed(() => {
  return radio1.value === "1"; // 1表示关闭状态
});

// 计算属性：功能数据区域是否禁用
const isFunctionDataDisabled = computed(() => {
  return radio2.value === "1"; // 1表示关闭状态
});

// 计算属性：是否正在加载状态
const isLoadingStatus = computed(() => {
  return radio1.value === "" || radio2.value === "" || hasPassword.value === null;
});

// 检查手机号是否存在（不显示错误提示，由调用方处理）
const checkPhoneExists = async (phone: string, tableName: string): Promise<boolean> => {
  try {
    const response = await request({
      url: '/mall-project/api/checkPhoneExists',
      method: 'post',
      data: {
        phone,
        tableName
      }
    }) as ApiResponse<string>;

    if (response && response.code === 200) {
      return response.data === "1"; // 1表示存在，0表示不存在
    } else {
      return false;
    }
  } catch (error) {
    console.error('检查手机号失败:', error);
    return false;
  }
};

// 校验逻辑：输入值必须 <= sourceValue 且 > 0
const isInputValid2 = computed(() => {
  const num = inputValue2.value;
  return (
    num !== null && !isNaN(num) && num > 0 && num <= Number(sourceValue.value)
  );
});

// 控制弹窗是否显示
const dialogVisible = ref(false);

// 表单数据
const form1 = ref({
  id: "",
  password: "",
  value: null as number | null,
});

// 校验逻辑：输入值必须 <= sourceValue 且 > 0
const isInputValid = computed(() => {
  const inputNum = inputValue.value;
  const baseNum = sourceValue.value;

  return (
    baseNum !== null && inputNum !== null && !isNaN(inputNum) && inputNum > 0 && inputNum <= baseNum
  );
});

// 处理输出按钮点击
const handleOutput = () => {
  // 验证量化值
  if (!sourceValue.value || sourceValue.value <= 0) {
    ElMessage.warning("请先输入有效的量化值");
    return;
  }

  // 验证输入值
  if (!inputQuantValue.value) {
    ElMessage.warning("请输入小于量化值的数值");
    return;
  }

  if (!isQuantValueValid.value) {
    ElMessage.warning("输入值必须大于0且小于等于量化值");
    return;
  }

  // 验证企业名称
  if (!selectedEnterpriseQuant.value) {
    ElMessage.warning("请选择企业名称");
    return;
  }

  // 验证手机号
  if (!phoneNumber.value) {
    ElMessage.warning("请输入手机号");
    return;
  }

  // 检查手机号格式和存在性
  if (!isPhoneValid.value) {
    ElMessage.warning("手机号不存在或手机号状态异常，请检查后重新输入");
    return;
  }

  // 格式正确，检查手机号是否存在
  checkPhoneExists(phoneNumber.value, selectedEnterpriseQuant.value).then(exists => {
    if (!exists) {
      ElMessage.warning("手机号不存在或手机号状态异常，请检查后重新输入");
      return;
    }

    // 手机号存在，显示密码验证弹窗
    showPasswordVerification(async () => {
    // 密码验证成功后调用API保存数据
    try {
      const response = await request({
        url: '/mall-project/api/outputQuantizationValue',
        method: 'post',
        data: {
          phone: phoneNumber.value,
          quantifyValue: parseFloat(inputQuantValue.value),
          tableName: selectedEnterpriseQuant.value
        }
      }) as ApiResponse;

      if (response && response.code === 200) {
        ElMessage.success(response.data || "输出成功");
        // 清空输入框
        inputQuantValue.value = "";
        phoneNumber.value = "";
        selectedEnterpriseQuant.value = "";
        // 异步刷新数据
        await refreshData();
      } else {
        ElMessage.error(response?.message || "输出失败");
      }
    } catch (error) {
      console.error('量化值输出失败:', error);
      if (error && error.message) {
        ElMessage.error(error.message);
      } else {
        ElMessage.error("输出失败，请稍后重试");
      }
    }
    });
  });
};

// 处理补贴金输出按钮点击
const handleSubsidyOutput = () => {
  // 验证补贴金
  if (!subsidyValue.value || subsidyValue.value <= 0) {
    ElMessage.warning("请先输入有效的补贴金");
    return;
  }

  // 验证输入值
  if (!inputSubsidyValue.value) {
    ElMessage.warning("请输入补贴金数值");
    return;
  }

  if (!isSubsidyValueValid.value) {
    ElMessage.warning("输入值必须大于0且小于等于补贴金");
    return;
  }

  // 验证企业名称
  if (!selectedEnterpriseSubsidy.value) {
    ElMessage.warning("请选择企业名称");
    return;
  }

  // 验证手机号
  if (!subsidyPhoneNumber.value) {
    ElMessage.warning("请输入手机号");
    return;
  }

  // 检查手机号是否存在（包含格式验证）
  if (!isSubsidyPhoneValid.value) {
    ElMessage.warning("手机号不存在或手机号状态异常，请检查后重新输入");
    return;
  }

  checkPhoneExists(subsidyPhoneNumber.value, selectedEnterpriseSubsidy.value).then(exists => {
    if (!exists) {
      ElMessage.warning("手机号不存在或手机号状态异常，请检查后重新输入");
      return;
    }

    // 手机号存在，显示密码验证弹窗
    showPasswordVerification(async () => {
    // 密码验证成功后调用API保存数据
    try {
      const response = await request({
        url: '/mall-project/api/outputSubsidy',
        method: 'post',
        data: {
          phone: subsidyPhoneNumber.value,
          subsidy: parseFloat(inputSubsidyValue.value),
          tableName: selectedEnterpriseSubsidy.value
        }
      }) as ApiResponse;

      if (response && response.code === 200) {
        ElMessage.success(response.data || "输出成功");
        // 清空输入框
        inputSubsidyValue.value = "";
        subsidyPhoneNumber.value = "";
        selectedEnterpriseSubsidy.value = "";
        // 异步刷新数据
        await refreshData();
      } else {
        ElMessage.error(response?.message || "输出失败");
      }
    } catch (error) {
      console.error('补贴金输出失败:', error);
      if (error && error.message) {
        ElMessage.error(error.message);
      } else {
        ElMessage.error("输出失败，请稍后重试");
      }
    }
    });
  });
};

// 处理功能数据确认按钮点击
const handleFunctionDataConfirm = () => {
  // 验证企业名称
  if (!selectedEnterpriseFunction.value) {
    ElMessage.warning("请选择企业名称");
    return;
  }

  // 验证手机号
  if (!message9.value) {
    ElMessage.warning("请输入手机号");
    return;
  }

  // 验证数值
  if (!message10.value) {
    ElMessage.warning("请输入数值");
    return;
  }

  const numValue = parseFloat(message10.value);
  if (isNaN(numValue) || numValue <= 0) {
    ElMessage.warning("请输入有效的正数");
    return;
  }

  // 检查手机号是否存在（包含格式验证）
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(message9.value)) {
    ElMessage.warning("手机号不存在或手机号状态异常，请检查后重新输入");
    return;
  }

  checkPhoneExists(message9.value, selectedEnterpriseFunction.value).then(exists => {
    if (!exists) {
      ElMessage.warning("手机号不存在或手机号状态异常，请检查后重新输入");
      return;
    }

    // 手机号存在，显示密码验证弹窗
    showPasswordVerification(async () => {
    // 密码验证成功后调用API保存数据
    try {
      const response = await request({
        url: '/mall-project/api/addFunctionDatas',
        method: 'put',
        data: {
          phone: message9.value,
          value: parseFloat(message10.value),
          tableName: selectedEnterpriseFunction.value
        }
      }) as ApiResponse;

      if (response && response.code === 200) {
        ElMessage.success(response.data || "新增成功！");
        // 清空输入框
        message9.value = "";
        message10.value = "";
        selectedEnterpriseFunction.value = "";
      } else {
        ElMessage.error(response?.message || "新增失败");
      }
    } catch (error) {
      console.error('功能数据保存失败:', error);
      if (error && error.message) {
        ElMessage.error(error.message);
      } else {
        ElMessage.error("新增失败，请稍后重试");
      }
    }
    });
  });
};

// 打开弹窗
const openDialog = () => {
  dialogVisible.value = true;
  console.log(11);
};

// 提交表单
const submitForm = () => {
  if (!form1.value.id.trim()) {
    ElMessage.warning("请输入用户ID");
    return;
  }

  if (!form1.value.password.trim()) {
    ElMessage.warning("请输入密码");
    return;
  }

  if (form1.value.password !== setPassword.value) {
    ElMessage.error("密码错误，与设置的密码不一致");
    return;
  }

  if (form1.value.value === null) {
    ElMessage.warning("请输入数值");
    return;
  }

  resultValue.value = form1.value.value.toString();
  ElMessage.success("提交成功");

  dialogVisible.value = false;
  form1.value = {
    id: "",
    password: "",
    value: null,
  };
};

// 当前选中项
const menuItems = ref([
  { label: "交易数据明细", path: "/transactionData" },
  { label: "关系链", path: "/about" },
  { label: "量化数", path: "/quantify" },
  { label: "量化值", path: "/quantizer" },
  { label: "核销数据", path: "/writeData" },
  { label: "量化值进化量", path: "/quant-evolution" },
  { label: "信用值进化量", path: "/credit-evolution" },
  { label: "授权表", path: "/authorization" },
]);
const menuItem = ref([
  { label: "系统设置", path: "/technicalDrainage" },
  { label: "系统更新", path: "/systemSetting" },
  { label: "状态数据", path: "/stateData" },
]);
const selectedItem = ref(null);

// 选择处理
const selectItem = (item: any) => {
  selectedItem.value = item;
  // 这里可以添加点击后的业务逻辑
};
// 格式化日期显示
const btn = (data: any) => {
  index.value = data;
};
const isActive = ref(false);
const power = () => {
  console.log(111);
  isActive.value = !isActive.value;
};
// 自动聚焦输入框
const focusInput = () => {
  nextTick(() => {
    passwordInput.value?.focus();
  });
};

// 关闭弹窗
const closeModal = () => {
  showModal.value = false;
  password.value = "";
  errorMessage.value = "";
};

// 提交验证
const handleSubmit = async () => {
  if (isSubmitting.value) return;

  if (!password.value.trim()) {
    errorMessage.value = "密码不能为空";
    focusInput();
    return;
  }

  isSubmitting.value = true;
  try {
    // 调用后台API验证密码
    const isValid = await verifyOperationPassword(password.value);

    if (isValid) {
      closeModal();
      // 执行验证成功后的回调
      if (passwordVerificationCallback.value) {
        passwordVerificationCallback.value();
        passwordVerificationCallback.value = null;
      }
    } else {
      errorMessage.value = "密码错误，请重新输入";
      password.value = "";
      focusInput();
    }
  } catch (error) {
    errorMessage.value = "验证失败，请重试";
  } finally {
    isSubmitting.value = false;
  }
};

// 键盘事件监听
const handleKeydown = (e: KeyboardEvent) => {
  if (showModal.value && e.key === "Escape") {
    closeModal();
  }
};

// 保存开关状态
const saveStatusSet = async (isEnabled: string, type: string) => {
  try {
    const response = await request({
      url: '/mall-project/api/saveOrUpdateStatusSet',
      method: 'put',
      data: {
        isEnabled,
        type
      }
    }) as ApiResponse;

    if (response && response.code === 200) {
      ElMessage.success(response.message || "保存成功");
    } else {
      ElMessage.error(response?.message || "保存失败");
    }
  } catch (error) {
    console.error('保存状态失败:', error);
    if (error && error.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error("保存失败，请稍后重试");
    }
  }
};

// 获取量化值数据
const getQuantizationValue = async () => {
  try {
    const response = await request({
      url: '/mall-project/api/quantizationValue',
      method: 'get'
    }) as ApiResponse;

    if (response && response.code === 200 && response.data) {
      sourceValue.value = parseFloat(response.data);
    } else {
      // 如果获取失败，设置默认值
      sourceValue.value = 100;
    }
  } catch (error) {
    console.error('获取量化值失败:', error);
    // 出错时设置默认值
    sourceValue.value = 100;
  }
};

// 获取补贴金数据
const getLostSubsidy = async () => {
  try {
    const response = await request({
      url: '/mall-project/api/lostSubsidy',
      method: 'get'
    }) as ApiResponse;

    if (response && response.code === 200 && response.data) {
      subsidyValue.value = parseFloat(response.data);
    } else {
      // 如果获取失败，设置默认值
      subsidyValue.value = 100;
    }
  } catch (error) {
    console.error('获取补贴金失败:', error);
    // 出错时设置默认值
    subsidyValue.value = 100;
  }
};

// 获取企业数据
const getEnterpriseOptions = async () => {
  try {
    const response = await request({
      url: '/mall-project/api/queryEnterprise',
      method: 'get'
    }) as ApiResponse<{cooperateEnterprise: Array<{id: string, name: string, tableName: string}>}>;

    if (response && response.code === 200 && response.data && response.data.cooperateEnterprise) {
      enterpriseOptions.value = response.data.cooperateEnterprise;
    } else {
      enterpriseOptions.value = [];
    }
  } catch (error) {
    console.error('获取企业数据失败:', error);
    enterpriseOptions.value = [];
  }
};



// 获取页面状态设置
const getStatusSet = async () => {
  try {
    // 并行获取两个区域的状态，提高加载速度
    const [quantResponse, funcResponse] = await Promise.all([
      // 获取量化值区域状态 (type: 0)
      request({
        url: '/mall-project/api/statusSet',
        method: 'post',
        data: {
          type: "0"
        }
      }) as Promise<ApiResponse<{is_enabled: number}>>,
      // 获取功能数据区域状态 (type: 1)
      request({
        url: '/mall-project/api/statusSet',
        method: 'post',
        data: {
          type: "1"
        }
      }) as Promise<ApiResponse<{is_enabled: number}>>
    ]);

    if (quantResponse && quantResponse.code === 200 && quantResponse.data) {
      radio1.value = quantResponse.data.is_enabled.toString();
    } else {
      // 如果获取失败，设置默认值
      radio1.value = "0";
    }

    if (funcResponse && funcResponse.code === 200 && funcResponse.data) {
      radio2.value = funcResponse.data.is_enabled.toString();
    } else {
      // 如果获取失败，设置默认值
      radio2.value = "0";
    }
  } catch (error) {
    console.error('获取状态失败:', error);
    // 出错时设置默认值，避免界面一直显示加载中
    if (radio1.value === "") radio1.value = "0";
    if (radio2.value === "") radio2.value = "0";
  }
};

// 验证操作密码
const verifyOperationPassword = async (password: string): Promise<boolean> => {
  try {
    const response = await request({
      url: '/mall-project/api/verifyOperationPassword',
      method: 'post',
      data: {
        password
      }
    }) as ApiResponse;

    if (response && response.code === 200) {
      return true;
    } else {
      if (response && response.message) {
        ElMessage.error(response.message);
      } else {
        ElMessage.error("验证失败");
      }
      return false;
    }
  } catch (error) {
    console.error('密码验证失败:', error);
    if (error && error.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error("验证失败，请稍后重试");
    }
    return false;
  }
};

// 显示密码验证弹窗并执行回调
const showPasswordVerification = (callback: () => void) => {
  // 重置密码输入框和错误信息
  password.value = "";
  errorMessage.value = "";
  showModal.value = true;

  // 设置验证成功后的回调
  passwordVerificationCallback.value = callback;

  // 自动聚焦到密码输入框
  nextTick(() => {
    passwordInput.value?.focus();
  });
};

// 密码验证成功后的回调函数
const passwordVerificationCallback = ref<(() => void) | null>(null);
// 刷新页面数据
const refreshData = async () => {
  try {
    // 并行刷新所有数据
    await Promise.all([
      getQuantizationValue(),
      getLostSubsidy(),
      getStatusSet(),
      getEnterpriseOptions()
    ]);
  } catch (error) {
    console.error('刷新数据失败:', error);
  }
};

// 检查用户密码设置状态
const checkPasswordStatus = async () => {
  try {
    const response = await request({
      url: '/mall-project/api/getOperationPasswordStatus',
      method: 'get'
    }) as ApiResponse<{phone?: string}>;

    if (response && response.code === 200) {
      // 如果phone不为空，说明用户已设置密码
      hasPassword.value = !!(response.data && response.data.phone);
    } else {
      // 默认未设置密码
      hasPassword.value = false;
    }
  } catch (error) {
    console.error('检查密码状态失败:', error);
    // 出错时默认未设置密码
    hasPassword.value = false;
  }
};

// 标记是否正在初始化，避免初始化时触发保存
const isInitializing = ref(true);

// 监听开关变化
watch(radio1, (newValue) => {
  // 只有在非初始化状态下才保存
  if (!isInitializing.value) {
    // 量化值开关变化时保存状态
    saveStatusSet(newValue, "0");
  }
});

watch(radio2, (newValue) => {
  // 只有在非初始化状态下才保存
  if (!isInitializing.value) {
    // 功能数据开关变化时保存状态
    saveStatusSet(newValue, "1");
  }
});

// 监听补贴金输入值变化，当输入大于补贴金时自动设置为补贴金
watch(inputSubsidyValue, (newValue) => {
  if (newValue && subsidyValue.value) {
    const inputNum = parseFloat(newValue);
    if (!isNaN(inputNum) && inputNum > subsidyValue.value) {
      // 当输入值大于补贴金时，自动设置为补贴金数值
      inputSubsidyValue.value = subsidyValue.value.toString();
    }
  }
});

// 生命周期
onMounted(async () => {
  window.addEventListener("keydown", handleKeydown);

  // 并行获取所有初始数据
  await Promise.all([
    // 检查密码设置状态
    checkPasswordStatus(),
    // 获取页面状态设置
    getStatusSet(),
    // 获取量化值
    getQuantizationValue(),
    // 获取补贴金
    getLostSubsidy(),
    // 获取企业数据
    getEnterpriseOptions()
  ]);

  // 初始化完成，允许监听器触发保存
  isInitializing.value = false;
});

onUnmounted(() => {
  window.removeEventListener("keydown", handleKeydown);
});
//设置密码
const openModal1 = (type: string) => {
  activeModal.value = type;
  showModal1.value = true;
};
const closeModal1 = () => {
  showModal.value = false;
  activeModal.value = "";
  // 清空表单
  Object.keys(form).forEach((key) => (form[key as keyof typeof form] = ""));
};
const handleSetPassword = async () => {
  // 验证密码一致性
  if (form.newPassword !== form.confirmPassword) {
    ElMessage.error("两次输入的密码不一致！");
    return;
  }

  // 验证密码长度
  if (!form.newPassword || form.newPassword.length < 6) {
    ElMessage.error("操作密码不能为空，且至少为6个字符");
    return;
  }

  try {
    // 调用API设置密码
    const response = await request({
      url: '/mall-project/api/saveOrUpdateOperationPassword',
      method: 'put',
      data: {
        password: form.newPassword
      }
    }) as ApiResponse;

    if (response && response.code === 200) {
      // 设置成功
      setPassword.value = form.newPassword;
      ElMessage.success(response.data || "密码设置成功！");
      closeModal1();
      // 重新检查密码状态，更新按钮显示
      checkPasswordStatus();
    } else {
      // 设置失败
      ElMessage.error(response?.message || "密码设置失败");
    }
  } catch (error) {
    // 处理错误响应
    console.error('设置密码失败:', error);
    if (error && error.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error("密码设置失败，请稍后重试");
    }
  }
};

const handleChangePassword = () => {
  /*if (form.oldPassword !== setPassword.value) {
    alert("原密码错误，请重新输入");
    return;
  }*/

  if (form.newPassword !== form.confirmNewPassword) {
    alert("两次输入的新密码不一致！");
    return;
  }

  // 更新全局密码
  setPassword.value = form.newPassword;

  alert("密码修改成功！");
  closeModal1();
};
const evolution1 = async () => {
  showModal3.value = true;
  inputValue.value = null;
  errorMessage.value = "";
  nextTick(() => {
    if (inputRef.value) inputRef.value.focus();
  });
};
const evolution2 = async () => {
  showModal2.value = true;
  await nextTick();
  inputRef.value?.focus(); // 自动聚焦输入框
};
//第一个关闭框
const closeModal2 = () => {
  showModal3.value = false;
  inputValue.value = null; // 清空临时输入
};
//第二个关闭框
const closeModal3 = () => {
  showModal2.value = false;
  inputValue2.value = null; // 清空临时输入
};

const confirmInput = () => {
  if (!isInputValid.value) {
    ElMessage.warning("请输入小于基准值的正整数");
    return;
  }
  if (inputValue.value !== null) {
    resultValue.value = inputValue.value.toString(); // 渲染到表单上
    ElMessage.success("输入有效：" + inputValue.value);
  }
  closeModal2();
};
//第二个模态框确定输入
const confirmInput1 = () => {
  if (!isInputValid2.value) {
    ElMessage.warning("请输入小于基准值的正整数");
    return;
  }
  if (inputValue2.value !== null) {
    resultValue1.value = inputValue2.value.toString(); // 渲染到表单上
    ElMessage.success("输入有效：" + inputValue2.value);
  }
  closeModal3();
};
</script>
<style lang="scss" scoped>
/* 基础样式 */
html,
body {
  height: 100vh;
  margin: 0;
  padding: 0;
  color: #fff;
}
.active {
  background-color: #2a48bf; /* 或者其他你想要的高亮颜色 */
  color: black; /* 确保文字颜色在背景色上清晰可见 */
}
@media (max-width: 768px) {
  .layout-container {
    flex-direction: column;
  }
  .sidebar1,
  .sidebar {
    width: 100%;
    height: auto;
  }
}
.line {
  width: 100%;
  height: 2px; /* 与状态数据一致 */
  background-color: #333; /* 与状态数据一致 */
  margin-top: 10px; /* 与状态数据一致 */
}
.function{
  font-size: 20px;
}

/* 两列布局样式 */
.two-column-layout {
  display: flex;
  gap: 50px;
  margin-top: 20px;
}

.left-column,
.right-column {
  flex: 1;
  margin-left: -30px;
}

/* 新增的输入行样式 */
.input-row {
  display: flex;
  align-items: center;
  margin: 20px 0;
  padding-left: 30px;
}

.input-label {
  display: inline-block;
  width: 200px;
  font-size: 14px;
  color: #333;
  margin-right: 10px;
}

.input-field {
  width: 300px;
  height: 40px;
}

.phone-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.right1 {
  width: 100%;
  // display: flex;
  flex-wrap: wrap; /* 允许换行 */
  height: 60px;
  margin-top: 30px;
  padding-left: 1300px;
  border-bottom: 3px solid  #000;
}

.result-box{
  margin-top: 30px;
}
.data{
  width: 100%;
  font-size: 20px;
  font-weight: bold;
  //margin-left: 50px;
  //border-bottom: 1px solid #000;
}
.top-right-buttons {
  position: absolute;
  right: 20px; // 距离右侧 20px
  top: 20px; // 距离顶部 20px
  display: flex;
  gap: 10px; // 按钮之间的间距
  z-index: 1000; // 确保按钮在其他内容之上
}
/* 功能数据区域整体样式 */
.right5 {
  height: 280px; /* 进一步增加下半部分高度 */
  margin-top: 20px;
  border-radius: 10px;
  box-shadow: 0px 10px 10px rgba(42, 72, 191, 0.3);
  padding-bottom: 20px;
}

.right5-top{
  display: flex;
  flex-direction: column;
  margin-left: 40px;
}

.sidebar {
  --active-bg: #2c3e50;
  --active-color: #ffffff;
  --hover-bg: #e9ecef;

  width: 240px;
  background: #f8f9fa;
  height: 100vh;
  border-right: 1px solid #dee2e6;

  .sidebar2 {
    list-style: none;
    padding: 20px 0;
    margin: 0;

    li {
      position: relative;
      transition: background-color 0.3s ease;

      a {
        display: block;
        // padding: 12px 24px;
        color: #2c3e50;
        text-decoration: none;
        transition: color 0.3s ease;
      }

      &:hover {
        background-color: var(--hover-bg);
      }

      &.active1 {
        background-color: var(--active-bg);
        border-left: 4px solid #3498db;

        a {
          color: var(--active-color);
          font-weight: 500;
        }
      }
    }
  }
}
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 300px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
}

.form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.button-group {
  margin-top: 20px;
  text-align: right;
}

.button-group button {
  // margin-left: 10px;
  padding: 8px 16px;
  cursor: pointer;
}

button {
  height: 40px;
  padding: 8px 16px;
  margin-right: 10px;
  cursor: pointer;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
}

button:hover {
  background: #0056b3;
}
.layout-container {
  height: 100vh;
  width: 100%;
  display: flex;
}
.sidebar2 li {
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.sidebar2 li a {
  color: #666;
  text-decoration: none;
}

.sidebar2 li.active {
  background: #2c3e50;
  border-left: 4px solid #3498db;
}

.sidebar2 li.active a {
  color: white;
}
.sidebar1,
.sidebar {
  // background: #f8f9fa; /* 侧边栏背景色 */
  padding: 15px;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  height: 100%;
  width: 150px;
  border: 1px solid #000;
}
.right5-footer{
  display: flex;
  flex-direction: column;
}
.right5-content {
  margin-left: 50px;
  margin-top: 20px;
  display: flex;
  flex-direction: row;
  span {
    display: inline-block;
    width: 200px;
    height: 40px;
  }
  .rate {
    margin-top: 10px;
    width: 200px;
    height: 40px;
  }
}

/* 企业名称独占一行的样式 */
.right5-content-full {
  margin-left: 50px;
  margin-top: 20px;
  display: flex;
  flex-direction: row;
  width: 100%;
  span {
    display: inline-block;
    width: 200px;
    height: 40px;
  }
  .rate {
    margin-top: 10px;
    width: 300px;
    height: 40px;
  }
}

/* 手机号和数值在同一行的容器样式 */
.right5-content-row {
  display: flex;
  flex-direction: row;
  width: 100%;
  gap: 20px;
  align-items: flex-end; /* 让按钮与输入框底部对齐 */
}

/* 确认按钮容器样式 */
.right5-content-btn {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  margin-left: 20px;
}

/* 确认按钮样式 */
.btn-confirm {
  width: 80px;
  height: 40px;
  margin-top: 30px; /* 与输入框对齐 */
}

.btn {
  margin-top: 30px;
  margin-left: 50px;
}
.btn2 {
  margin-top: 90px;
  margin-left: 50px;
}


.sidebar3,
.sidebar2 {
  list-style: none;
  padding: 0;
  margin: 0;
  height: 100%;
  overflow-y: auto;
}

.sidebar3 li,
.sidebar2 li {
  color: #ffff;
  padding: 10px 25px;

margin-bottom: 5px;
  border-radius: 4px;
  transition: background 0.3s;
}

.sidebar3 li:hover,
.sidebar2 li:hover {
  background: #e9ecef;
}
.sidebar3 li:after,
.sidebar2 li:after {
  background: #e9ecef;
}

.sidebar3 a,
.sidebar2 a {
  text-decoration: none;
  color: #fff;
  display: block;
  text-align: center;
}

.main-content {
  position: relative;
  flex: 1;
  //   padding: 20px;
  //margin-left: 10%;
  width: 100%;
  height: 100%;
}
.right4 {
  width: 100%;
  // display: flex;
  flex-wrap: wrap; /* 允许换行 */
  height: 480px; /* 减少上半部分高度 */
  box-shadow: 5px 15px 10px #ccc;
  margin-top: 30px;
  border-radius: 10px;
  box-shadow: 0px 15px 15px #2a48bf;
}
.right4-content {
  flex: 1 0 calc(50% - 50px); /* 基础宽度50% 减半间距 */
  height: 80px;
  // background: #f0f0f0;
  // border: 1px solid #ddd;
  box-sizing: border-box;
  border-radius: 8px;
  transition: transform 0.3s;
  margin-left: 50px;
}
//保存按钮
.save {
  border-radius: 5px;
  margin-left: 200px;
  margin-top: 70px;
}
.content {
  width: 100%;
  border-bottom: 1px solid #000;
}
.rate1 {
  height: 25px;
  width: 300px;
  margin-top: 10px;
  margin-bottom: 20px;
  border: 1px solid #ccc;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 30px;
}
// 确认按钮
.container {
  position: relative;
}

.modal-mask {
  position: fixed;
  z-index: 9998;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-container {
  background: white;
  width: 90%;
  max-width: 400px;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.33);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.close-btn {
  font-size: 24px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0 8px;
}

.modal-body input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 10px;
}

.error-msg {
  color: #ff4444;
  font-size: 0.9em;
  height: 20px;
}

.modal-footer {
  margin-top: 20px;
  text-align: right;
}
.cancel {
  margin-right: -50px;
}
.confirm {
  margin-right: 90px;
}
.confirm-btn,
.cancel-btn {
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.confirm-btn {
  background-color: #1890ff;
  color: white;
  border: none;
}

.confirm-btn:disabled {
  background-color: #8cc8ff;
  cursor: not-allowed;
}

.cancel-btn {
  background-color: #f0f0f0;
  border: 1px solid #d9d9d9;
}
.action-btn {
  padding: 12px 24px;
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
}

/* 加载状态样式 */
.loading-placeholder {
  color: #999;
  font-size: 14px;
  padding: 8px 0;
  display: flex;
  align-items: center;
}

.loading-placeholder::before {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>